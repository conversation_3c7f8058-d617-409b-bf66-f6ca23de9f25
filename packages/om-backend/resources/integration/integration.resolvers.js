const { model: AccountModel } = require('../account/account.model');
const { model: CampaignModel } = require('../campaign/campaign.model');
const MailChimpAdapter = require('../../services/integrations/mailChimp');
const SalesAutopilotAdapter = require('../../services/integrations/salesAutopilot');
const CampaignMonitorAdapter = require('../../services/integrations/campaignMonitor');
const MiniCrmAdapter = require('../../services/integrations/miniCrm');
const MailUpAdapter = require('../../services/integrations/mailUp');
const ShopRenterAdapter = require('../../services/integrations/shopRenter');
const redisClient = require('../../services/ioRedisAdapter');
const KlaviyoAdapter = require('../../services/integrations/klaviyo');
const KlaviyoOAuthAdapter = require('../../services/integrations/klaviyoOAuth');

async function asyncForEach(array, callback) {
  for (let index = 0; index < array.length; index++) {
    await callback(array[index], index, array);
  }
}

const getIntegrationLists = async (_, { integrationType, integrationId }, { userId, log }) => {
  const authData = await getAuthDataById(integrationId, userId);
  log.info({ authData });
  if (!authData) return [];

  const IntegrationAdapter = require(`../../services/integrations/${integrationType}`);
  const integrationAdapter = new IntegrationAdapter(authData);
  const lists = await integrationAdapter.getLists();
  log.info({ lists });

  return lists;
};

const getCampaignIntegrationLists = async (_, { integrations }, { userId, log }) => {
  const lists = [];
  const listIdKeys = [
    'listId',
    'listType',
    'mailingList',
    'groupId',
    'addressbookId',
    'databaseId',
  ];
  let storedLists = await redisClient.get(`integrationsLists:${userId}`);
  log.info(`cached integration lists for ${userId}`, storedLists);
  storedLists = storedLists ? JSON.parse(storedLists) : [];
  // get all campaigns that are using this integration and project their list IDs
  const campaigns = await CampaignModel(userId)
    .find(
      { 'settings.integrations.settings.key': { $in: listIdKeys } },
      { 'settings.integrations.settings': 1, 'settings.integrations.id': 1 },
    )
    .sort({ createdAt: -1 });
  // create unique array of used lists by campaigns
  const usedListIds = [];
  campaigns.forEach((campaign) => {
    const integrations = campaign.settings.integrations;
    integrations.forEach(({ settings, id }) => {
      const list = settings.find((setting) => listIdKeys.includes(setting.key));
      const clientId = settings.find((setting) => setting.key === 'clientId');
      if (list && list.value && !usedListIds.find((usedList) => usedList.listId === list.value)) {
        usedListIds.push({
          listId: list.value,
          integrationId: id,
          clientId: clientId ? clientId.value : null,
        });
      }
    });
  });
  log.info(`used lists by campaigns for user ${userId}`, usedListIds);
  await asyncForEach(usedListIds, async ({ listId, integrationId, clientId }) => {
    let integrationList = storedLists.find((list) => list.id === listId);
    const integration = integrations.find((integration) => integrationId.equals(integration._id));
    // if not in redis, get all lists from provider
    if (!integrationList) {
      log.info(
        `list with ID ${listId} and type ${integration.type} not found in cache, getting lists from API`,
      );
      try {
        const IntegrationAdapter = require(`../../services/integrations/${integration.type}`);
        const integrationAdapter = new IntegrationAdapter(integration.data);
        let integrationLists = [];
        if (integration.type === 'campaignMonitor') {
          integrationLists = await integrationAdapter.getListByClientId(clientId);
        } else if (integrationAdapter.getLists) {
          integrationLists = await integrationAdapter.getLists();
        }
        integrationList = integrationLists.find((list) => list.id === listId);
        storedLists = [...storedLists, ...integrationLists];
      } catch (e) {
        log.error(`integration API communication error for ${integration.type}`, e);
      }
    }
    // need to check again since the list might have been removed from provider
    if (integrationList) {
      lists.push({
        id: listId,
        name: integrationList.name,
        type: integration.type,
        providerId: integration._id,
      });
    }
  });

  await redisClient.setex(`integrationsLists:${userId}`, JSON.stringify(lists), 24 * 60 * 60); // 1 day

  return lists;
};

const pingGlobalIntegration = async (_, { integrationType, integrationId }, { userId }) => {
  const authData = await getAuthDataById(integrationId, userId);
  if (!authData) return false;

  const IntegrationAdapter = require(`../../services/integrations/${integrationType}`);
  const integrationAdapter = new IntegrationAdapter(authData);
  try {
    const canConnect = await integrationAdapter.ping();
    return canConnect;
  } catch (error) {
    return false;
  }
};

const getIntegrationData = async (_, { integrationType, integrationId }, { userId }) => {
  const authData = await getAuthDataById(integrationId, userId);
  if (!authData) return {};

  const IntegrationAdapter = require(`../../services/integrations/${integrationType}`);
  const integrationAdapter = new IntegrationAdapter(authData);
  const data = await integrationAdapter.getData();

  return data;
};

const getIntegrationFields = async (_, { integrationType, integrationId, listId }, { userId }) => {
  const authData = await getAuthDataById(integrationId, userId);
  if (!authData || !listId) return [];

  const IntegrationAdapter = require(`../../services/integrations/${integrationType}`);
  const integrationAdapter = new IntegrationAdapter(authData);
  const fields = await integrationAdapter.getFields(listId);

  return fields;
};

const getMailChimpGroupsAndFields = async (_, { integrationId, listId }, { userId }) => {
  const ret = { groups: [], fields: [], reqFields: [], groupNames: [] };

  if (!integrationId || !listId) return ret;

  const authData = await getAuthDataById(integrationId, userId);
  if (!authData) return ret;

  const mailChimpAdapter = new MailChimpAdapter(authData);
  try {
    // removed Promise.all because we hit rate limit (10 interest groups are requested simultaneously)
    const groups = await mailChimpAdapter.getListGroups(listId);
    const fields = await mailChimpAdapter.getListFields(listId);
    const reqFields = await mailChimpAdapter.getRequiredFields(listId);
    const cats = await mailChimpAdapter.getInterestCategories(listId);

    ret.groups = groups;
    ret.fields = fields;
    ret.reqFields = reqFields;
    ret.groupNames = cats.map((c) => ({ id: c.id, title: c.title }));
  } catch (e) {
    console.log(`@@@error`, e);
  }

  return ret;
};

const getSalesAutopilotForms = async (_, { integrationId, listId }, { userId }) => {
  const authData = await getAuthDataById(integrationId, userId);
  if (!authData) return [];

  const salesAutopilotAdapter = new SalesAutopilotAdapter(authData);
  const forms = await salesAutopilotAdapter.getForms(listId);

  return forms;
};

const getSalesAutopilotListFields = async (_, { integrationId, listId }, { userId }) => {
  const authData = await getAuthDataById(integrationId, userId);
  if (!authData) return [];

  const salesAutopilotAdapter = new SalesAutopilotAdapter(authData);
  const forms = await salesAutopilotAdapter.getFields(listId);

  return forms;
};

const getAuthDataById = async (integrationId, userId) => {
  const response = await AccountModel.findOne(
    { databaseId: userId, 'settings.integrations._id': integrationId },
    { _id: 0, 'settings.integrations.$': 1 },
  );
  if (!response) return false;
  const {
    settings: {
      integrations: [{ data }],
    },
  } = response;
  return data;
};

const getCampaignMonitorClients = async (_, { integrationId }, { userId }) => {
  const authData = await getAuthDataById(integrationId, userId);
  if (!authData) return [];

  const campaignMonitorAdapter = new CampaignMonitorAdapter(authData);
  const clients = await campaignMonitorAdapter.getClients();

  return clients;
};

const getCampaignMonitorLists = async (_, { integrationId, clientId }, { userId }) => {
  const authData = await getAuthDataById(integrationId, userId);
  if (!authData || !clientId) return [];

  const campaignMonitorAdapter = new CampaignMonitorAdapter(authData);
  const lists = await campaignMonitorAdapter.getListByClientId(clientId);

  return lists;
};

const getMiniCrmDatas = async (_, { integrationId, categoryId }, { userId }) => {
  const authData = await getAuthDataById(integrationId, userId);
  if (!authData || !categoryId) return [];

  const miniCrmAdapter = new MiniCrmAdapter(authData);
  const datas = await miniCrmAdapter.getDatas(categoryId);

  return datas;
};

const getMailUpGroups = async (_, { integrationId, listId }, { userId }) => {
  const authData = await getAuthDataById(integrationId, userId);
  if (!authData || !listId) return [];

  const mailUpAdapter = new MailUpAdapter(authData);
  const datas = await mailUpAdapter.getGroups(listId);

  return datas;
};

const getCleverReachForms = async (_, { integrationType, integrationId, groupId }, { userId }) => {
  const authData = await getAuthDataById(integrationId, userId);
  if (!authData || !groupId) return [];

  const IntegrationAdapter = require(`../../services/integrations/${integrationType}`);
  const integrationAdapter = new IntegrationAdapter(authData);
  const fields = await integrationAdapter.getForms(groupId);

  return fields;
};

const getShoprenterLanguages = async (
  _,
  { integrationId, apiUrl, username, password },
  { userId },
) => {
  let authData;
  if (integrationId) authData = await getAuthDataById(integrationId, userId);
  else authData = { apiUrl, username, password };
  const integrationAdapter = new ShopRenterAdapter(authData);
  const languages = await integrationAdapter.getLanguages();

  return languages;
};

const getKlaviyoSegmentsAndList = async (_, { integrationId }, { userId, log }) => {
  const authData = await getAuthDataById(integrationId, userId);
  if (!authData) return [];

  if (authData.type === 'klaviyoOAuth') {
    const klaviyoOAuthAdapter = new KlaviyoOAuthAdapter(authData);
    try {
      return klaviyoOAuthAdapter.getSegmentsAndList();
    } catch (error) {
      log.warn({
        message: 'Failed to get segments and list from Klaviyo OAuth',
        error,
      });
      return [];
    }
  }

  const klaviyoAdapter = new KlaviyoAdapter(authData);

  try {
    return klaviyoAdapter.getSegmentsAndList();
  } catch (error) {
    log.warn({
      message: 'Failed to get segments and list from Klaviyo',
      error,
    });
    return [];
  }
};

module.exports = {
  Query: {
    getIntegrationLists,
    getIntegrationData,
    getIntegrationFields,
    getMailChimpGroupsAndFields,
    getSalesAutopilotForms,
    getSalesAutopilotListFields,
    getCampaignMonitorClients,
    getCampaignMonitorLists,
    getMiniCrmDatas,
    getMailUpGroups,
    getCleverReachForms,
    pingGlobalIntegration,
    getShoprenterLanguages,
    getCampaignIntegrationLists,
    getKlaviyoSegmentsAndList,
  },
  Mutation: {},
};
