<template lang="pug">
.integration-type-component
  .klaviyo-specific-info.mb-3
    p.mb-1 Public Key: {{ maskData(integration.data.publicApiKey) }}

    .integration-actions.d-flex.align-items-center.mr-1.mt-3
      template(v-if="!isConnected")
        UilTimesCircle(size="1.25em" color="#E4252D")
        .auth-success.ml-1 {{ $t('integrationsPage.disconnected') }}
      template(v-else-if="isConnected")
        UilCheckCircle(size="1.25em" color="#2CC896")
        .auth-success.mx-1 {{ $t('integrationsPage.connected') }}
        .integration-actions.d-flex.align-items-center.ml-2
          OmButton(primary small icon="linkBroken" :loading="loading" @click="disconnect") {{ $t('integrationsPage.disconnect') }}
</template>

<script>
  import { UilCheckCircle, UilTimesCircle } from '@iconscout/vue-unicons';

  export default {
    name: 'KlaviyoOAuthTypeComponent',
    components: {
      UilCheckCircle,
      UilTimesCircle,
    },
    props: {
      integrationType: {
        type: String,
        required: true,
      },
      integration: {
        type: Object,
        required: true,
      },
    },
    data() {
      return {
        loading: false,
      };
    },
    computed: {
      isConnected() {
        return this.integration.data.access_token;
      },
    },
    methods: {
      maskData(data) {
        if (!data) return this.$t('integrationsPage.notAvailable');
        if (data.length <= 8) return '***';
        return `${data.substring(0, 4)}***${data.substring(data.length - 4)}`;
      },
      async disconnect() {
        this.loading = true;
        try {
          const { data: result } = await this.$axios.post(
            '/integration-oauth/klaviyoOAuth/disconnect',
            { integrationId: this.integration._id },
          );

          if (result.success) {
            this.$notify({
              type: 'success',
              text: this.$t('integrationsPage.disconnectSuccess'),
            });

            this.$emit('refreshIntegrationList');
          } else {
            this.$notify({
              type: 'error',
              text: this.$t('integrationsPage.disconnectError'),
            });
          }
        } catch (e) {
          console.error('Disconnect error:', e);
          this.$notify({
            type: 'error',
            text: this.$t('integrationsPage.disconnectError'),
          });
        } finally {
          this.loading = false;
        }
      },
    },
  };
</script>

<style lang="sass" scoped>
  .integration-type-component
    .card
      border: 1px solid #e0e0e0

      .card-title
        color: #007bff
        margin-bottom: 0.5rem

      .card-text
        font-size: 0.875rem

    .list-group-item
      border: 1px solid #e0e0e0

      .list-info
        strong
          color: #495057

        small
          color: #6c757d

      .list-actions
        .btn
          font-size: 0.75rem
          padding: 0.25rem 0.5rem
</style>
