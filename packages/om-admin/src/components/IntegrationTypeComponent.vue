<template lang="pug">
.integration-type-component
  .integration-list
    .integration-item.mb-3
      .row.align-items-center
        .col-md-8
          .integration-type-specific.mt-1(v-if="hasTypeSpecificContent")
            component(
              :is="getTypeSpecificComponent"
              :integration-type="integrationType"
              :integration="integration"
              :campaigns-by-status="campaignsByStatus"
              @refreshIntegrationList="$emit('refreshIntegrationList')"
            )
</template>

<script>
  import { capitalizeFirstLetter } from '@/util';

  export default {
    name: 'IntegrationTypeComponent',
    components: {
      KlaviyoOAuthTypeComponent: () =>
        import('@/components/IntegrationTypeComponents/KlaviyoOAuthTypeComponent.vue'),
    },
    props: {
      integrationType: {
        type: String,
        required: true,
      },
      integration: {
        type: Object,
        required: true,
      },
      campaignsByStatus: {
        type: Object,
        default: () => ({}),
      },
    },
    computed: {
      hasTypeSpecificContent() {
        return this.getTypeSpecificComponent !== null;
      },
      getTypeSpecificComponent() {
        const componentMap = {
          klaviyoOAuth: 'KlaviyoOAuthTypeComponent',
        };
        return componentMap[this.integrationType] || null;
      },
    },
    methods: {
      capitalizeFirstLetter,
      integrationName(integration) {
        return integration.data.name;
      },
    },
  };
</script>

<style lang="sass" scoped>
  .integration-type-component

    .integration-info
      .detail-item
        margin-bottom: 0.25rem
        font-size: 0.875rem

        strong
          color: #495057

        span
          color: #6c757d

    .integration-actions
      .action-buttons
        .btn
          font-size: 0.75rem
          padding: 0.25rem 0.5rem

    .integration-status
      .badge
        font-size: 0.75rem
</style>
