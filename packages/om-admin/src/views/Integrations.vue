<template lang="pug">
div
  .brand-wrapper.re-integration
    om-heading(h1) {{ getTitle }}
    .wrapper
      .alert-block-re-integration(v-if="!isAllReIntegrated")
        alert(type="warning" :integrationType="getIntegrationType")
      .deprecated-integrations
        DeprecatedIntegrations(
          :integrationType="getIntegrationType"
          :filteredIntegrations="filteredIntegrations"
          :campaignsByStatus="campaignsByStatus"
        )
      template(v-if="!needReIntegration")
        .d-flex.align-items-center.mt-4(v-if="refreshing")
          .spinner-border.spinner-border-sm.mr-2
          span {{ $t('integrationsPage.refreshing') }}
        .integrations-accordion.col-md-8.mt-4(v-if="integrationsByType.length > 0 && !refreshing")
          accordion
            accordion-item(
              v-for="(integration, index) in integrationsByType"
              :key="integration._id"
              :step="null"
              :open-default="index === 0"
            )
              template(#name)
                .d-flex.justify-content-between.align-items-center.w-100
                  span {{ integration.data.name }}
              .integration-type-content
                IntegrationTypeComponent(
                  :integration-type="getIntegrationType"
                  :integration="integration"
                  :campaigns-by-status="campaignsByStatus"
                  :key="integration._id"
                  @refreshIntegrationList="handleFetchAccount"
                )
</template>
<script>
  import { capitalizeFirstLetter } from '@/util';
  import { mapActions, mapGetters } from 'vuex';
  import { getConfigForIntegration } from '@om/integrations';
  import reIntegrationMixin from '@/components/ReIntegration/reIntegration';
  import Accordion, { AccordionItem } from '@/components/Elements/Accordion';

  export default {
    components: {
      Alert: () => import('@/components/ReIntegration/Alerts/Alert.vue'),
      Success: () => import('@/components/ReIntegration/Alerts/Success.vue'),
      DeprecatedIntegrations: () => import('@/components/ReIntegration/DeprecatedIntegrations.vue'),
      Accordion,
      AccordionItem,
      IntegrationTypeComponent: () => import('@/components/IntegrationTypeComponent.vue'),
    },
    mixins: [reIntegrationMixin],
    data() {
      return {
        integrationsByType: [],
        campaignsByStatus: {},
        refreshing: false,
      };
    },
    computed: {
      ...mapGetters(['integrations']),
      getIntegrationType() {
        return this.$route.query.integrationType;
      },
      filteredIntegrations() {
        const query = this.$route.query;

        if (!Object.keys(query).length) {
          return this.integrations;
        }

        return this.integrations.filter((integration) => {
          if (query.integrationType && integration.type !== query.integrationType) {
            return false;
          }

          if (query.integrationTypes) {
            const types = query.integrationTypes.split(',').map((t) => t.trim());
            if (!types.includes(integration.type)) {
              return false;
            }
          }

          return true;
        });
      },
      isDeprecated() {
        return getConfigForIntegration(this.getIntegrationType).deprecated || false;
      },
      needReIntegration() {
        return this.filteredIntegrations.length > 0 && this.isDeprecated;
      },
      getTitle() {
        let integration = this.getIntegrationType;
        if (!integration) {
          return this.$t('integrationsPage.allIntegrations');
        }

        if (integration === 'klaviyoOAuth') {
          integration = 'klaviyo';
        }
        return this.$t('integrationsPage.title', {
          integrationType: capitalizeFirstLetter(integration),
        });
      },
    },
    async mounted() {
      this.integrationsByType = this.integrations.filter((integration) => {
        return integration.type === this.getIntegrationType;
      });

      if (this.filteredIntegrations?.length) {
        const integrationIds = this.filteredIntegrations.map((i) => i._id);
        const campaigns = await this.getCampaignsByIntegrationId(integrationIds);
        this.campaignsByStatus = campaigns;
      }
    },
    methods: {
      ...mapActions(['fetchAccount']),
      capitalizeFirstLetter,
      async handleFetchAccount() {
        this.refreshing = true;
        try {
          // Fetch updated account data
          await this.fetchAccount();

          // Refresh the local integration list
          this.integrationsByType = this.integrations.filter((integration) => {
            return integration.type === this.getIntegrationType;
          });

          // Refresh campaigns by status if needed
          if (this.filteredIntegrations?.length) {
            const integrationIds = this.filteredIntegrations.map((i) => i._id);
            const campaigns = await this.getCampaignsByIntegrationId(integrationIds);
            this.campaignsByStatus = campaigns;
          }
        } catch (error) {
          console.error('Error refreshing integrations:', error);
          this.$notify({
            type: 'error',
            text: this.$t('integrationsPage.refreshError'),
          });
        } finally {
          this.refreshing = false;
        }
      },
    },
  };
</script>
<style lang="sass">
  @import '@/components/ReIntegration/reIntegration.sass'
</style>
